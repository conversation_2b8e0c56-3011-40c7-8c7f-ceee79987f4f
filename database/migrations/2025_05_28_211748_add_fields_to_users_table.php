<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('first_name')->after('name')->nullable();
            $table->string('last_name')->after('first_name')->nullable();
            $table->string('phone')->after('email')->nullable();
            $table->date('date_of_birth')->after('phone')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->after('date_of_birth')->nullable();
            $table->text('address')->after('gender')->nullable();
            $table->string('city')->after('address')->nullable();
            $table->string('country')->after('city')->nullable();
            $table->string('timezone')->after('country')->default('UTC');
            $table->string('avatar')->after('timezone')->nullable();
            $table->boolean('is_active')->after('avatar')->default(true);
            $table->timestamp('last_login_at')->after('is_active')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'first_name', 'last_name', 'phone', 'date_of_birth', 'gender',
                'address', 'city', 'country', 'timezone', 'avatar', 'is_active', 'last_login_at'
            ]);
        });
    }
}
