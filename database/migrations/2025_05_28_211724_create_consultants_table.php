<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConsultantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('consultants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title')->nullable(); // Dr., Prof., etc.
            $table->text('bio')->nullable();
            $table->text('experience')->nullable();
            $table->string('education')->nullable();
            $table->string('certifications')->nullable();
            $table->string('languages')->nullable();
            $table->decimal('hourly_rate', 8, 2)->default(0);
            $table->integer('years_experience')->default(0);
            $table->string('profile_image')->nullable();
            $table->json('availability_hours')->nullable(); // Store weekly availability
            $table->string('timezone')->default('UTC');
            $table->enum('status', ['pending', 'approved', 'suspended', 'rejected'])->default('pending');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->decimal('rating', 3, 2)->default(0);
            $table->integer('total_consultations')->default(0);
            $table->boolean('is_available')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('consultants');
    }
}
