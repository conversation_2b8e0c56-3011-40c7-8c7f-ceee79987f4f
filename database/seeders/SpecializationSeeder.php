<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Specialization;

class SpecializationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $specializations = [
            [
                'name' => 'Business Strategy',
                'description' => 'Strategic planning, business development, and growth strategies for companies of all sizes.',
                'icon' => 'fas fa-chart-line',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Marketing & Sales',
                'description' => 'Digital marketing, sales strategies, brand development, and customer acquisition.',
                'icon' => 'fas fa-bullhorn',
                'color' => '#EF4444',
                'sort_order' => 2,
            ],
            [
                'name' => 'Technology Consulting',
                'description' => 'Software development, system architecture, digital transformation, and IT strategy.',
                'icon' => 'fas fa-laptop-code',
                'color' => '#10B981',
                'sort_order' => 3,
            ],
            [
                'name' => 'Financial Advisory',
                'description' => 'Financial planning, investment strategies, accounting, and business finance.',
                'icon' => 'fas fa-dollar-sign',
                'color' => '#F59E0B',
                'sort_order' => 4,
            ],
            [
                'name' => 'Human Resources',
                'description' => 'Talent acquisition, employee development, organizational culture, and HR policies.',
                'icon' => 'fas fa-users',
                'color' => '#8B5CF6',
                'sort_order' => 5,
            ],
            [
                'name' => 'Legal Consulting',
                'description' => 'Business law, contract review, compliance, and legal risk management.',
                'icon' => 'fas fa-gavel',
                'color' => '#6B7280',
                'sort_order' => 6,
            ],
            [
                'name' => 'Operations Management',
                'description' => 'Process optimization, supply chain management, and operational efficiency.',
                'icon' => 'fas fa-cogs',
                'color' => '#F97316',
                'sort_order' => 7,
            ],
            [
                'name' => 'Career Coaching',
                'description' => 'Professional development, career transitions, leadership coaching, and skill development.',
                'icon' => 'fas fa-user-tie',
                'color' => '#EC4899',
                'sort_order' => 8,
            ],
            [
                'name' => 'Healthcare Consulting',
                'description' => 'Healthcare management, medical practice optimization, and healthcare technology.',
                'icon' => 'fas fa-heartbeat',
                'color' => '#DC2626',
                'sort_order' => 9,
            ],
            [
                'name' => 'Education & Training',
                'description' => 'Educational program development, training design, and learning strategies.',
                'icon' => 'fas fa-graduation-cap',
                'color' => '#059669',
                'sort_order' => 10,
            ],
        ];

        foreach ($specializations as $specialization) {
            Specialization::create($specialization);
        }
    }
}
