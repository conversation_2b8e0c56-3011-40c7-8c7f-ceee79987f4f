<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Consultant;
use App\Models\Specialization;
use App\Models\AvailabilitySlot;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Admin User
        $admin = User::create([
            'name' => 'Admin User',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone' => '+1234567890',
            'city' => 'New York',
            'country' => 'USA',
            'timezone' => 'America/New_York',
            'is_active' => true,
        ]);
        $admin->assignRole('admin');

        // Create sample consultants
        $consultants = [
            [
                'user' => [
                    'name' => 'Dr. <PERSON>',
                    'first_name' => '<PERSON>',
                    'last_name' => 'Johnson',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567891',
                    'city' => 'San Francisco',
                    'country' => 'USA',
                    'timezone' => 'America/Los_Angeles',
                ],
                'consultant' => [
                    'title' => 'Dr.',
                    'bio' => 'Experienced business strategist with over 15 years in corporate consulting. Specialized in helping startups and SMEs develop sustainable growth strategies.',
                    'experience' => 'Former McKinsey consultant, led strategy initiatives for Fortune 500 companies.',
                    'education' => 'MBA from Stanford, PhD in Business Strategy',
                    'certifications' => 'Certified Management Consultant (CMC)',
                    'languages' => 'English, Spanish, French',
                    'years_experience' => 15,
                    'hourly_rate' => 150.00,
                    'status' => 'approved',
                    'approved_at' => now(),
                    'rating' => 4.8,
                    'total_consultations' => 127,
                ],
                'specializations' => ['business-strategy', 'marketing-sales'],
            ],
            [
                'user' => [
                    'name' => 'Michael Chen',
                    'first_name' => 'Michael',
                    'last_name' => 'Chen',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567892',
                    'city' => 'Seattle',
                    'country' => 'USA',
                    'timezone' => 'America/Los_Angeles',
                ],
                'consultant' => [
                    'title' => 'Mr.',
                    'bio' => 'Senior software architect and technology consultant with expertise in cloud computing, microservices, and digital transformation.',
                    'experience' => 'Former Principal Engineer at Amazon, led multiple large-scale system migrations.',
                    'education' => 'MS Computer Science from MIT',
                    'certifications' => 'AWS Solutions Architect Professional, Google Cloud Professional',
                    'languages' => 'English, Mandarin',
                    'years_experience' => 12,
                    'hourly_rate' => 180.00,
                    'status' => 'approved',
                    'approved_at' => now(),
                    'rating' => 4.9,
                    'total_consultations' => 89,
                ],
                'specializations' => ['technology-consulting'],
            ],
            [
                'user' => [
                    'name' => 'Emily Rodriguez',
                    'first_name' => 'Emily',
                    'last_name' => 'Rodriguez',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567893',
                    'city' => 'Austin',
                    'country' => 'USA',
                    'timezone' => 'America/Chicago',
                ],
                'consultant' => [
                    'title' => 'Ms.',
                    'bio' => 'Financial advisor and investment strategist helping businesses optimize their financial performance and investment portfolios.',
                    'experience' => 'Former VP at Goldman Sachs, specialized in corporate finance and M&A.',
                    'education' => 'MBA Finance from Wharton, CFA Charter',
                    'certifications' => 'Certified Financial Planner (CFP), Chartered Financial Analyst (CFA)',
                    'languages' => 'English, Spanish',
                    'years_experience' => 10,
                    'hourly_rate' => 160.00,
                    'status' => 'approved',
                    'approved_at' => now(),
                    'rating' => 4.7,
                    'total_consultations' => 156,
                ],
                'specializations' => ['financial-advisory'],
            ],
        ];

        foreach ($consultants as $consultantData) {
            // Create user
            $user = User::create(array_merge($consultantData['user'], [
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'is_active' => true,
            ]));
            $user->assignRole('consultant');

            // Create consultant profile
            $consultant = Consultant::create(array_merge($consultantData['consultant'], [
                'user_id' => $user->id,
                'approved_by' => $admin->id,
            ]));

            // Assign specializations
            $specializations = Specialization::whereIn('slug', $consultantData['specializations'])->get();
            $consultant->specializations()->attach($specializations);

            // Create availability slots for the next 30 days
            $this->createAvailabilitySlots($consultant);
        }

        // Create regular users
        $regularUsers = [
            [
                'name' => 'John Smith',
                'first_name' => 'John',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'phone' => '+1234567894',
                'city' => 'Boston',
                'country' => 'USA',
            ],
            [
                'name' => 'Lisa Wang',
                'first_name' => 'Lisa',
                'last_name' => 'Wang',
                'email' => '<EMAIL>',
                'phone' => '+1234567895',
                'city' => 'Los Angeles',
                'country' => 'USA',
            ],
        ];

        foreach ($regularUsers as $userData) {
            $user = User::create(array_merge($userData, [
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'timezone' => 'America/New_York',
                'is_active' => true,
            ]));
            $user->assignRole('user');
        }
    }

    private function createAvailabilitySlots($consultant)
    {
        $startDate = Carbon::today();
        $endDate = Carbon::today()->addDays(30);

        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekends for this example
            if ($date->isWeekend()) {
                continue;
            }

            // Create morning slots (9 AM - 12 PM)
            for ($hour = 9; $hour < 12; $hour++) {
                AvailabilitySlot::create([
                    'consultant_id' => $consultant->id,
                    'date' => $date->format('Y-m-d'),
                    'start_time' => sprintf('%02d:00', $hour),
                    'end_time' => sprintf('%02d:30', $hour),
                    'status' => 'available',
                    'is_available' => true,
                ]);

                AvailabilitySlot::create([
                    'consultant_id' => $consultant->id,
                    'date' => $date->format('Y-m-d'),
                    'start_time' => sprintf('%02d:30', $hour),
                    'end_time' => sprintf('%02d:00', $hour + 1),
                    'status' => 'available',
                    'is_available' => true,
                ]);
            }

            // Create afternoon slots (2 PM - 5 PM)
            for ($hour = 14; $hour < 17; $hour++) {
                AvailabilitySlot::create([
                    'consultant_id' => $consultant->id,
                    'date' => $date->format('Y-m-d'),
                    'start_time' => sprintf('%02d:00', $hour),
                    'end_time' => sprintf('%02d:30', $hour),
                    'status' => 'available',
                    'is_available' => true,
                ]);

                AvailabilitySlot::create([
                    'consultant_id' => $consultant->id,
                    'date' => $date->format('Y-m-d'),
                    'start_time' => sprintf('%02d:30', $hour),
                    'end_time' => sprintf('%02d:00', $hour + 1),
                    'status' => 'available',
                    'is_available' => true,
                ]);
            }
        }
    }
}
