<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Consultant permissions
            'view consultants',
            'create consultants',
            'edit consultants',
            'delete consultants',
            'approve consultants',

            // Booking permissions
            'view bookings',
            'create bookings',
            'edit bookings',
            'delete bookings',
            'cancel bookings',

            // Specialization permissions
            'view specializations',
            'create specializations',
            'edit specializations',
            'delete specializations',

            // Availability permissions
            'manage availability',
            'view availability',

            // Admin permissions
            'access admin panel',
            'view reports',
            'manage system settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin role
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Consultant role
        $consultantRole = Role::create(['name' => 'consultant']);
        $consultantRole->givePermissionTo([
            'view bookings',
            'edit bookings',
            'cancel bookings',
            'manage availability',
            'view availability',
            'edit consultants', // Can edit their own profile
        ]);

        // User role (regular users)
        $userRole = Role::create(['name' => 'user']);
        $userRole->givePermissionTo([
            'view consultants',
            'view specializations',
            'create bookings',
            'view bookings',
            'cancel bookings',
        ]);
    }
}
