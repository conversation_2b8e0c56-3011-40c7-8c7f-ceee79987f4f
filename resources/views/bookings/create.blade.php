<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Book Consultation - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-blue-600">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('consultants.index') }}" class="text-gray-700 hover:text-blue-600">Consultants</a>
                    <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600">Dashboard</a>
                    <a href="{{ route('bookings.index') }}" class="text-gray-700 hover:text-blue-600">My Bookings</a>
                    
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-gray-700 hover:text-blue-600">
                            <i class="fas fa-sign-out-alt mr-1"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <a href="{{ route('consultants.show', $consultant) }}" class="text-blue-600 hover:text-blue-700 mb-4 inline-block">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to {{ $consultant->full_name }}
            </a>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Book Your Consultation</h1>
            <p class="text-gray-600">Complete the form below to book your 30-minute free consultation.</p>
        </div>

        <!-- Error Messages -->
        @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <ul class="list-disc list-inside">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <!-- Error Message -->
        @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {{ session('error') }}
        </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Booking Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-8">
                    <form method="POST" action="{{ route('bookings.store') }}">
                        @csrf
                        <input type="hidden" name="consultant_id" value="{{ $consultant->id }}">
                        <input type="hidden" name="availability_slot_id" value="{{ $slot->id }}">

                        <!-- Consultation Topic -->
                        <div class="mb-6">
                            <label for="consultation_topic" class="block text-sm font-medium text-gray-700 mb-2">
                                What would you like to discuss? *
                            </label>
                            <input type="text" 
                                   id="consultation_topic" 
                                   name="consultation_topic" 
                                   value="{{ old('consultation_topic') }}"
                                   placeholder="e.g., Business strategy for startup growth"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                   required>
                            <p class="text-sm text-gray-500 mt-1">Brief description of your consultation topic</p>
                        </div>

                        <!-- Additional Notes -->
                        <div class="mb-6">
                            <label for="user_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Additional Notes (Optional)
                            </label>
                            <textarea id="user_notes" 
                                      name="user_notes" 
                                      rows="4"
                                      placeholder="Any specific questions or background information you'd like to share..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">{{ old('user_notes') }}</textarea>
                            <p class="text-sm text-gray-500 mt-1">Help your consultant prepare for the session</p>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-6">
                            <div class="flex items-start">
                                <input type="checkbox" 
                                       id="terms" 
                                       name="terms" 
                                       class="mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       required>
                                <label for="terms" class="text-sm text-gray-700">
                                    I agree to the <a href="#" class="text-blue-600 hover:text-blue-700">Terms of Service</a> 
                                    and <a href="#" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>. 
                                    I understand this is a free 30-minute consultation session.
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex space-x-4">
                            <button type="submit" 
                                    class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition font-medium">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Confirm Booking
                            </button>
                            <a href="{{ route('consultants.show', $consultant) }}" 
                               class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Booking Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h3>
                    
                    <!-- Consultant Info -->
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">{{ $consultant->full_name }}</h4>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 mr-1">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $consultant->rating ? '' : 'text-gray-300' }} text-xs"></i>
                                    @endfor
                                </div>
                                <span class="text-xs text-gray-600">({{ $consultant->rating }})</span>
                            </div>
                        </div>
                    </div>

                    <!-- Appointment Details -->
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Date:</span>
                            <span class="font-medium">{{ \Carbon\Carbon::parse($slot->date)->format('M j, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Time:</span>
                            <span class="font-medium">{{ $slot->time_range }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Duration:</span>
                            <span class="font-medium">30 minutes</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Type:</span>
                            <span class="font-medium">Video Call</span>
                        </div>
                    </div>

                    <!-- Price -->
                    <div class="border-t border-gray-200 pt-4 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-medium text-gray-900">Total:</span>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-green-600">FREE</div>
                                <div class="text-sm text-gray-500 line-through">${{ number_format($consultant->hourly_rate * 0.5, 2) }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Specializations -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Specializations:</h4>
                        <div class="flex flex-wrap gap-1">
                            @foreach($consultant->specializations->take(3) as $spec)
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {{ $spec->name }}
                            </span>
                            @endforeach
                        </div>
                    </div>

                    <!-- What to Expect -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">What to Expect:</h4>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li>• 30-minute video consultation</li>
                            <li>• Professional advice & guidance</li>
                            <li>• Actionable recommendations</li>
                            <li>• Follow-up resources if needed</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="text-2xl font-bold mb-4">
                <i class="fas fa-calendar-check mr-2"></i>
                ConsultBook
            </div>
            <p class="text-gray-400">&copy; {{ date('Y') }} ConsultBook. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
