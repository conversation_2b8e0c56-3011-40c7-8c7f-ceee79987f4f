<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>My Bookings - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-blue-600">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('consultants.index') }}" class="text-gray-700 hover:text-blue-600">Consultants</a>
                    <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600">Dashboard</a>
                    <a href="{{ route('bookings.index') }}" class="text-blue-600 font-medium">My Bookings</a>
                    
                    <div class="relative">
                        <button class="flex items-center text-gray-700 hover:text-blue-600">
                            <i class="fas fa-user-circle text-xl mr-2"></i>
                            {{ Auth::user()->full_name }}
                        </button>
                    </div>
                    
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-gray-700 hover:text-blue-600">
                            <i class="fas fa-sign-out-alt mr-1"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">My Bookings</h1>
            <p class="text-gray-600">Manage your consultation appointments and view your booking history.</p>
        </div>

        <!-- Success Message -->
        @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            {{ session('success') }}
        </div>
        @endif

        <!-- Error Message -->
        @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {{ session('error') }}
        </div>
        @endif

        <!-- Upcoming Bookings -->
        @if($upcomingBookings->count() > 0)
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Upcoming Consultations</h2>
            </div>
            
            <div class="divide-y divide-gray-200">
                @foreach($upcomingBookings as $booking)
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-gray-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">{{ $booking->consultant->full_name }}</h3>
                                <p class="text-gray-600">{{ $booking->formatted_scheduled_time }}</p>
                                <p class="text-sm text-gray-500">{{ $booking->consultation_topic }}</p>
                                <div class="mt-2">
                                    <span class="px-2 py-1 bg-{{ $booking->status_color }}-100 text-{{ $booking->status_color }}-800 text-xs rounded-full">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-3">
                            <a href="{{ route('bookings.show', $booking) }}" 
                               class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                                View Details
                            </a>
                            
                            @if($booking->canBeCancelled())
                            <form method="POST" action="{{ route('bookings.cancel', $booking) }}" class="inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" 
                                        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition"
                                        onclick="return confirm('Are you sure you want to cancel this booking?')">
                                    Cancel
                                </button>
                            </form>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Past Bookings -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Booking History</h2>
            </div>
            
            @if($pastBookings->count() > 0)
            <div class="divide-y divide-gray-200">
                @foreach($pastBookings as $booking)
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-gray-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">{{ $booking->consultant->full_name }}</h3>
                                <p class="text-gray-600">{{ $booking->formatted_scheduled_time }}</p>
                                <p class="text-sm text-gray-500">{{ $booking->consultation_topic }}</p>
                                <div class="mt-2 flex items-center space-x-3">
                                    <span class="px-2 py-1 bg-{{ $booking->status_color }}-100 text-{{ $booking->status_color }}-800 text-xs rounded-full">
                                        {{ ucfirst($booking->status) }}
                                    </span>
                                    
                                    @if($booking->rating)
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400 mr-2">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $booking->rating ? '' : 'text-gray-300' }}"></i>
                                            @endfor
                                        </div>
                                        <span class="text-sm text-gray-600">{{ $booking->rating }}/5</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-3">
                            <a href="{{ route('bookings.show', $booking) }}" 
                               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition">
                                View Details
                            </a>
                            
                            @if($booking->canBeReviewed())
                            <a href="{{ route('bookings.review', $booking) }}" 
                               class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition">
                                Leave Review
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($pastBookings->hasPages())
            <div class="p-6 border-t border-gray-200">
                {{ $pastBookings->links() }}
            </div>
            @endif
            @else
            <div class="p-6 text-center">
                <i class="fas fa-calendar-alt text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No bookings yet</h3>
                <p class="text-gray-600 mb-6">Start by booking your first consultation with one of our expert consultants.</p>
                <a href="{{ route('consultants.index') }}" 
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition">
                    Browse Consultants
                </a>
            </div>
            @endif
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="text-2xl font-bold mb-4">
                <i class="fas fa-calendar-check mr-2"></i>
                ConsultBook
            </div>
            <p class="text-gray-400">&copy; {{ date('Y') }} ConsultBook. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
