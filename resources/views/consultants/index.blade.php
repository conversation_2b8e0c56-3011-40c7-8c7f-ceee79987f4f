<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Find Consultants - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-blue-600">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('consultants.index') }}" class="text-blue-600 font-medium">Consultants</a>
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-blue-600">About</a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-blue-600">Contact</a>
                    
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600">Dashboard</a>
                        <a href="{{ route('bookings.index') }}" class="text-gray-700 hover:text-blue-600">My Bookings</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-700 hover:text-blue-600">Logout</button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600">Login</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Sign Up</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Find Expert Consultants</h1>
            <p class="text-gray-600">Browse our network of professional consultants and book your consultation today.</p>
        </div>

        <!-- Filters -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <form method="GET" action="{{ route('consultants.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search by name or expertise..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Specialization -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                    <select name="specialization" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Specializations</option>
                        @foreach($specializations as $spec)
                        <option value="{{ $spec->slug }}" {{ request('specialization') == $spec->slug ? 'selected' : '' }}>
                            {{ $spec->name }}
                        </option>
                        @endforeach
                    </select>
                </div>

                <!-- Rating -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Rating</label>
                    <select name="rating" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Any Rating</option>
                        <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4+ Stars</option>
                        <option value="4.5" {{ request('rating') == '4.5' ? 'selected' : '' }}>4.5+ Stars</option>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        <option value="rating" {{ request('sort') == 'rating' ? 'selected' : '' }}>Highest Rated</option>
                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name</option>
                        <option value="experience" {{ request('sort') == 'experience' ? 'selected' : '' }}>Most Experienced</option>
                        <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                    </select>
                </div>

                <div class="md:col-span-4">
                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                        <i class="fas fa-search mr-2"></i>
                        Search Consultants
                    </button>
                    <a href="{{ route('consultants.index') }}" class="ml-4 text-gray-600 hover:text-gray-800">
                        Clear Filters
                    </a>
                </div>
            </form>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($consultants as $consultant)
            <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-6">
                <!-- Profile Header -->
                <div class="text-center mb-4">
                    <div class="w-20 h-20 mx-auto mb-3 bg-gray-300 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-2xl text-gray-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">{{ $consultant->full_name }}</h3>
                    
                    <!-- Rating -->
                    <div class="flex items-center justify-center mt-2">
                        <div class="flex text-yellow-400">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $consultant->rating ? '' : 'text-gray-300' }}"></i>
                            @endfor
                        </div>
                        <span class="ml-2 text-gray-600">({{ $consultant->rating }})</span>
                    </div>
                </div>

                <!-- Bio -->
                <p class="text-gray-600 text-sm mb-4">{{ Str::limit($consultant->bio, 120) }}</p>

                <!-- Specializations -->
                <div class="mb-4">
                    <div class="flex flex-wrap gap-2">
                        @foreach($consultant->specializations->take(3) as $spec)
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            {{ $spec->name }}
                        </span>
                        @endforeach
                    </div>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                    <div>
                        <i class="fas fa-briefcase mr-1"></i>
                        {{ $consultant->years_experience }} years exp.
                    </div>
                    <div>
                        <i class="fas fa-users mr-1"></i>
                        {{ $consultant->total_consultations }} sessions
                    </div>
                </div>

                <!-- Action Button -->
                <div class="text-center">
                    <a href="{{ route('consultants.show', $consultant) }}" 
                       class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition inline-block">
                        View Profile & Book
                    </a>
                </div>
            </div>
            @empty
            <div class="col-span-full text-center py-12">
                <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No consultants found</h3>
                <p class="text-gray-600 mb-4">Try adjusting your search criteria or browse all consultants.</p>
                <a href="{{ route('consultants.index') }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                    View All Consultants
                </a>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($consultants->hasPages())
        <div class="mt-8">
            {{ $consultants->links() }}
        </div>
        @endif
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="text-2xl font-bold mb-4">
                <i class="fas fa-calendar-check mr-2"></i>
                ConsultBook
            </div>
            <p class="text-gray-400 mb-4">
                Professional consultation booking platform connecting experts with those who need guidance.
            </p>
            <p class="text-gray-400">&copy; {{ date('Y') }} ConsultBook. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
