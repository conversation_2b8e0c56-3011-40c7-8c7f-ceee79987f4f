<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $consultant->full_name }} - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-blue-600">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('consultants.index') }}" class="text-blue-600 font-medium">Consultants</a>
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-blue-600">About</a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-blue-600">Contact</a>
                    
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600">Dashboard</a>
                        <a href="{{ route('bookings.index') }}" class="text-gray-700 hover:text-blue-600">My Bookings</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-700 hover:text-blue-600">Logout</button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600">Login</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Sign Up</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('consultants.index') }}" class="text-blue-600 hover:text-blue-700">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Consultants
            </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Consultant Profile -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-8">
                    <!-- Header -->
                    <div class="flex items-start space-x-6 mb-8">
                        <div class="w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-4xl text-gray-600"></i>
                        </div>
                        
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $consultant->full_name }}</h1>
                            
                            <!-- Rating -->
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400 mr-2">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $consultant->rating ? '' : 'text-gray-300' }}"></i>
                                    @endfor
                                </div>
                                <span class="text-gray-600">({{ $consultant->rating }}) • {{ $consultant->total_consultations }} consultations</span>
                            </div>

                            <!-- Specializations -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                @foreach($consultant->specializations as $spec)
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                                    {{ $spec->name }}
                                </span>
                                @endforeach
                            </div>

                            <!-- Stats -->
                            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                    <i class="fas fa-briefcase mr-2"></i>
                                    {{ $consultant->years_experience }} years experience
                                </div>
                                <div>
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    {{ $consultant->user->city }}, {{ $consultant->user->country }}
                                </div>
                                <div>
                                    <i class="fas fa-language mr-2"></i>
                                    {{ $consultant->languages }}
                                </div>
                                <div>
                                    <i class="fas fa-clock mr-2"></i>
                                    {{ $consultant->user->timezone }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bio -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">About</h2>
                        <p class="text-gray-700 leading-relaxed">{{ $consultant->bio }}</p>
                    </div>

                    <!-- Experience -->
                    @if($consultant->experience)
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Experience</h2>
                        <p class="text-gray-700 leading-relaxed">{{ $consultant->experience }}</p>
                    </div>
                    @endif

                    <!-- Education -->
                    @if($consultant->education)
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Education</h2>
                        <p class="text-gray-700 leading-relaxed">{{ $consultant->education }}</p>
                    </div>
                    @endif

                    <!-- Certifications -->
                    @if($consultant->certifications)
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Certifications</h2>
                        <p class="text-gray-700 leading-relaxed">{{ $consultant->certifications }}</p>
                    </div>
                    @endif
                </div>

                <!-- Reviews -->
                @if($reviews->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-8 mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Client Reviews</h2>
                    
                    <div class="space-y-6">
                        @foreach($reviews as $review)
                        <div class="border-b border-gray-200 pb-6 last:border-b-0">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-gray-600"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">{{ $review->user->full_name }}</h4>
                                        <div class="flex text-yellow-400">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $review->rating ? '' : 'text-gray-300' }}"></i>
                                            @endfor
                                        </div>
                                    </div>
                                    @if($review->review)
                                    <p class="text-gray-700">{{ $review->review }}</p>
                                    @endif
                                    <p class="text-sm text-gray-500 mt-2">{{ $review->reviewed_at->format('M j, Y') }}</p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Booking Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Book a Consultation</h3>
                    
                    <div class="mb-6">
                        <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                            <div class="text-2xl font-bold text-green-600 mb-1">FREE</div>
                            <div class="text-sm text-green-700">30-minute consultation</div>
                        </div>
                    </div>

                    @auth
                        @if($availableSlots->count() > 0)
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Available Times</h4>
                            <div class="space-y-3 max-h-64 overflow-y-auto">
                                @foreach($availableSlots->take(7) as $date => $slots)
                                <div>
                                    <div class="text-sm font-medium text-gray-700 mb-2">
                                        {{ \Carbon\Carbon::parse($date)->format('M j, Y') }}
                                    </div>
                                    <div class="grid grid-cols-2 gap-2">
                                        @foreach($slots->take(4) as $slot)
                                        <a href="{{ route('bookings.create', ['consultant_id' => $consultant->id, 'slot_id' => $slot->id]) }}" 
                                           class="text-center py-2 px-3 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition">
                                            {{ \Carbon\Carbon::parse($slot->start_time)->format('g:i A') }}
                                        </a>
                                        @endforeach
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        
                        <button class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition font-medium">
                            View All Available Times
                        </button>
                        @else
                        <div class="text-center py-8">
                            <i class="fas fa-calendar-times text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">No available slots at the moment</p>
                            <p class="text-sm text-gray-500">Please check back later or contact the consultant directly.</p>
                        </div>
                        @endif
                    @else
                        <div class="text-center py-8">
                            <i class="fas fa-user-lock text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">Please log in to book a consultation</p>
                            <a href="{{ route('login') }}" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition font-medium inline-block">
                                Login to Book
                            </a>
                        </div>
                    @endauth

                    <!-- Contact Info -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="font-medium text-gray-900 mb-3">Need Help?</h4>
                        <p class="text-sm text-gray-600 mb-3">
                            Have questions about this consultation? Feel free to reach out.
                        </p>
                        <a href="{{ route('contact') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                            Contact Support →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="text-2xl font-bold mb-4">
                <i class="fas fa-calendar-check mr-2"></i>
                ConsultBook
            </div>
            <p class="text-gray-400 mb-4">
                Professional consultation booking platform connecting experts with those who need guidance.
            </p>
            <p class="text-gray-400">&copy; {{ date('Y') }} ConsultBook. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>
