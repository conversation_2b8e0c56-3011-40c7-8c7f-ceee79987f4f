<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name', 'Laravel') }} - Professional Consultation Booking</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-blue-600">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('consultants.index') }}" class="text-gray-700 hover:text-blue-600">Consultants</a>
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-blue-600">About</a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-blue-600">Contact</a>
                    
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600">Dashboard</a>
                        <a href="{{ route('bookings.index') }}" class="text-gray-700 hover:text-blue-600">My Bookings</a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-700 hover:text-blue-600">Logout</button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-blue-600">Login</a>
                        <a href="{{ route('register') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Sign Up</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl font-bold mb-6">
                Book Expert Consultations
                <span class="block text-blue-200">In Just 30 Minutes</span>
            </h1>
            <p class="text-xl mb-8 max-w-3xl mx-auto">
                Connect with top-rated consultants across various specializations. 
                Get professional advice and solutions for your business challenges.
            </p>
            <div class="space-x-4">
                <a href="{{ route('consultants.index') }}" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition">
                    Browse Consultants
                </a>
                @guest
                <a href="{{ route('register') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition">
                    Get Started Free
                </a>
                @endguest
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-4xl font-bold text-blue-600">{{ $stats['total_consultants'] }}</div>
                    <div class="text-gray-600 mt-2">Expert Consultants</div>
                </div>
                <div>
                    <div class="text-4xl font-bold text-blue-600">{{ $stats['total_consultations'] }}</div>
                    <div class="text-gray-600 mt-2">Completed Sessions</div>
                </div>
                <div>
                    <div class="text-4xl font-bold text-blue-600">{{ $stats['total_specializations'] }}</div>
                    <div class="text-gray-600 mt-2">Specializations</div>
                </div>
                <div>
                    <div class="text-4xl font-bold text-blue-600">{{ number_format($stats['average_rating'], 1) }}</div>
                    <div class="text-gray-600 mt-2">Average Rating</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Specializations Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Popular Specializations</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Find experts in various fields ready to help you solve your challenges
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($specializations as $specialization)
                <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" 
                             style="background-color: {{ $specialization->color }}20;">
                            <i class="{{ $specialization->icon }} text-2xl" style="color: {{ $specialization->color }};"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $specialization->name }}</h3>
                        <p class="text-gray-600 text-sm mb-4">{{ Str::limit($specialization->description, 80) }}</p>
                        <a href="{{ route('consultants.index', ['specialization' => $specialization->slug]) }}" 
                           class="text-blue-600 hover:text-blue-700 font-medium">
                            Find Consultants →
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Featured Consultants -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Top-Rated Consultants</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Meet our highest-rated experts ready to help you succeed
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($featuredConsultants as $consultant)
                <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition">
                    <div class="text-center mb-4">
                        <div class="w-20 h-20 mx-auto mb-4 bg-gray-300 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-2xl text-gray-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900">{{ $consultant->full_name }}</h3>
                        <div class="flex items-center justify-center mt-2">
                            <div class="flex text-yellow-400">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $consultant->rating ? '' : 'text-gray-300' }}"></i>
                                @endfor
                            </div>
                            <span class="ml-2 text-gray-600">({{ $consultant->rating }})</span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-gray-600 text-sm mb-3">{{ Str::limit($consultant->bio, 120) }}</p>
                        <div class="flex flex-wrap gap-2">
                            @foreach($consultant->specializations->take(2) as $spec)
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                {{ $spec->name }}
                            </span>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            {{ $consultant->years_experience }} years exp.
                        </div>
                        <a href="{{ route('consultants.show', $consultant) }}" 
                           class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                            Book Now
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
            
            <div class="text-center mt-12">
                <a href="{{ route('consultants.index') }}" 
                   class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition">
                    View All Consultants
                </a>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">How It Works</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Get expert advice in just three simple steps
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-2xl font-bold">1</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Choose a Consultant</h3>
                    <p class="text-gray-600">Browse our expert consultants and find the perfect match for your needs</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-2xl font-bold">2</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Book a Session</h3>
                    <p class="text-gray-600">Select an available time slot and book your 30-minute consultation</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-2xl font-bold">3</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Get Expert Advice</h3>
                    <p class="text-gray-600">Connect with your consultant and get the guidance you need</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="text-2xl font-bold mb-4">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </div>
                    <p class="text-gray-400">
                        Professional consultation booking platform connecting experts with those who need guidance.
                    </p>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('consultants.index') }}" class="text-gray-400 hover:text-white">Find Consultants</a></li>
                        <li><a href="{{ route('about') }}" class="text-gray-400 hover:text-white">About Us</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-400 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Connect</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} ConsultBook. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
