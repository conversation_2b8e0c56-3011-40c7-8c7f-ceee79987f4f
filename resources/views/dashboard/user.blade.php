<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Dashboard - {{ config('app.name') }}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{{ route('home') }}" class="text-2xl font-bold text-blue-600">
                        <i class="fas fa-calendar-check mr-2"></i>
                        ConsultBook
                    </a>
                </div>
                
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600">Home</a>
                    <a href="{{ route('consultants.index') }}" class="text-gray-700 hover:text-blue-600">Consultants</a>
                    <a href="{{ route('dashboard') }}" class="text-blue-600 font-medium">Dashboard</a>
                    <a href="{{ route('bookings.index') }}" class="text-gray-700 hover:text-blue-600">My Bookings</a>
                    
                    <div class="relative">
                        <button class="flex items-center text-gray-700 hover:text-blue-600">
                            <i class="fas fa-user-circle text-xl mr-2"></i>
                            {{ Auth::user()->full_name }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <!-- Dropdown would go here -->
                    </div>
                    
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-gray-700 hover:text-blue-600">
                            <i class="fas fa-sign-out-alt mr-1"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome back, {{ Auth::user()->first_name }}!</h1>
            <p class="text-gray-600">Manage your consultations and track your progress.</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-calendar-alt text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_bookings'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Completed</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['completed_consultations'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Upcoming</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['upcoming_bookings'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-star text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Reviews</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['pending_reviews'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Upcoming Bookings -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-900">Upcoming Consultations</h2>
                        <a href="{{ route('bookings.index') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                            View All
                        </a>
                    </div>
                </div>
                
                <div class="p-6">
                    @forelse($upcomingBookings as $booking)
                    <div class="flex items-center justify-between py-4 {{ !$loop->last ? 'border-b border-gray-100' : '' }}">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-gray-600"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">{{ $booking->consultant->full_name }}</p>
                                <p class="text-sm text-gray-600">{{ $booking->formatted_scheduled_time }}</p>
                                <p class="text-xs text-gray-500">{{ $booking->consultation_topic }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 bg-{{ $booking->status_color }}-100 text-{{ $booking->status_color }}-800 text-xs rounded-full">
                                {{ ucfirst($booking->status) }}
                            </span>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8">
                        <i class="fas fa-calendar-plus text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">No upcoming consultations</p>
                        <a href="{{ route('consultants.index') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition">
                            Book a Consultation
                        </a>
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900">Recent Activity</h2>
                </div>
                
                <div class="p-6">
                    @forelse($recentBookings as $booking)
                    <div class="flex items-center py-4 {{ !$loop->last ? 'border-b border-gray-100' : '' }}">
                        <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-gray-600"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">{{ $booking->consultant->full_name }}</p>
                            <p class="text-sm text-gray-600">{{ $booking->formatted_scheduled_time }}</p>
                            <div class="flex items-center mt-1">
                                <span class="px-2 py-1 bg-{{ $booking->status_color }}-100 text-{{ $booking->status_color }}-800 text-xs rounded-full">
                                    {{ ucfirst($booking->status) }}
                                </span>
                                @if($booking->canBeReviewed())
                                <a href="{{ route('bookings.review', $booking) }}" class="ml-2 text-blue-600 hover:text-blue-700 text-xs">
                                    Leave Review
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8">
                        <i class="fas fa-history text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">No recent activity</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('consultants.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition">
                    <div class="p-3 bg-blue-100 rounded-full mr-4">
                        <i class="fas fa-search text-blue-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Find Consultants</p>
                        <p class="text-sm text-gray-600">Browse expert consultants</p>
                    </div>
                </a>

                <a href="{{ route('bookings.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition">
                    <div class="p-3 bg-green-100 rounded-full mr-4">
                        <i class="fas fa-calendar text-green-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">My Bookings</p>
                        <p class="text-sm text-gray-600">View all consultations</p>
                    </div>
                </a>

                <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition">
                    <div class="p-3 bg-purple-100 rounded-full mr-4">
                        <i class="fas fa-user-cog text-purple-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Profile Settings</p>
                        <p class="text-sm text-gray-600">Update your profile</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</body>
</html>
