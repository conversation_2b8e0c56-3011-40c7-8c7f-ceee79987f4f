<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Consultant;
use App\Models\AvailabilitySlot;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of user's bookings.
     */
    public function index()
    {
        $user = Auth::user();

        $upcomingBookings = $user->bookings()
            ->with(['consultant.user', 'availabilitySlot'])
            ->upcoming()
            ->active()
            ->orderBy('scheduled_at')
            ->get();

        $pastBookings = $user->bookings()
            ->with(['consultant.user', 'availabilitySlot'])
            ->past()
            ->orderBy('scheduled_at', 'desc')
            ->paginate(10);

        return view('bookings.index', compact('upcomingBookings', 'pastBookings'));
    }

    /**
     * Show the form for creating a new booking.
     */
    public function create(Request $request)
    {
        $consultant = Consultant::findOrFail($request->consultant_id);
        $slot = AvailabilitySlot::findOrFail($request->slot_id);

        // Verify slot is available
        if (!$slot->canAccommodate(30)) {
            return redirect()->back()->with('error', 'This time slot is no longer available.');
        }

        return view('bookings.create', compact('consultant', 'slot'));
    }

    /**
     * Store a newly created booking.
     */
    public function store(Request $request)
    {
        $request->validate([
            'consultant_id' => 'required|exists:consultants,id',
            'availability_slot_id' => 'required|exists:availability_slots,id',
            'consultation_topic' => 'required|string|max:255',
            'user_notes' => 'nullable|string|max:1000',
        ]);

        $consultant = Consultant::findOrFail($request->consultant_id);
        $slot = AvailabilitySlot::findOrFail($request->availability_slot_id);

        // Double-check slot availability
        if (!$slot->canAccommodate(30)) {
            return redirect()->back()->with('error', 'This time slot is no longer available.');
        }

        DB::transaction(function () use ($request, $consultant, $slot) {
            // Create the booking
            $booking = Booking::create([
                'user_id' => Auth::id(),
                'consultant_id' => $consultant->id,
                'availability_slot_id' => $slot->id,
                'scheduled_at' => Carbon::parse($slot->date . ' ' . $slot->start_time),
                'duration_minutes' => 30,
                'consultation_topic' => $request->consultation_topic,
                'user_notes' => $request->user_notes,
                'status' => 'pending',
            ]);

            // Mark slot as booked
            $slot->markAsBooked();
        });

        return redirect()->route('bookings.index')
            ->with('success', 'Your consultation has been booked successfully! You will receive a confirmation email shortly.');
    }

    /**
     * Display the specified booking.
     */
    public function show(Booking $booking)
    {
        $this->authorize('view', $booking);

        $booking->load(['consultant.user', 'availabilitySlot', 'user']);

        return view('bookings.show', compact('booking'));
    }

    /**
     * Cancel a booking.
     */
    public function cancel(Request $request, Booking $booking)
    {
        $this->authorize('cancel', $booking);

        if (!$booking->canBeCancelled()) {
            return redirect()->back()->with('error', 'This booking cannot be cancelled.');
        }

        $request->validate([
            'cancellation_reason' => 'nullable|string|max:500',
        ]);

        $booking->cancel($request->cancellation_reason, Auth::id());

        return redirect()->route('bookings.index')
            ->with('success', 'Your booking has been cancelled successfully.');
    }

    /**
     * Show the form for reviewing a booking.
     */
    public function review(Booking $booking)
    {
        $this->authorize('review', $booking);

        if (!$booking->canBeReviewed()) {
            return redirect()->back()->with('error', 'This booking cannot be reviewed.');
        }

        return view('bookings.review', compact('booking'));
    }

    /**
     * Store a review for a booking.
     */
    public function storeReview(Request $request, Booking $booking)
    {
        $this->authorize('review', $booking);

        if (!$booking->canBeReviewed()) {
            return redirect()->back()->with('error', 'This booking cannot be reviewed.');
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:1000',
        ]);

        $booking->addReview($request->rating, $request->review);

        return redirect()->route('bookings.show', $booking)
            ->with('success', 'Thank you for your review!');
    }
}
