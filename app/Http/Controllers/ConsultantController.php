<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Consultant;
use App\Models\Specialization;
use App\Models\AvailabilitySlot;
use Carbon\Carbon;

class ConsultantController extends Controller
{
    /**
     * Display a listing of consultants.
     */
    public function index(Request $request)
    {
        $query = Consultant::with(['user', 'specializations'])
            ->approved()
            ->available();

        // Filter by specialization
        if ($request->filled('specialization')) {
            $query->whereHas('specializations', function ($q) use ($request) {
                $q->where('slug', $request->specialization);
            });
        }

        // Filter by rating
        if ($request->filled('rating')) {
            $query->where('rating', '>=', $request->rating);
        }

        // Search by name or bio
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%");
                })->orWhere('bio', 'like', "%{$search}%");
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'rating');
        switch ($sortBy) {
            case 'name':
                $query->join('users', 'consultants.user_id', '=', 'users.id')
                    ->orderBy('users.first_name');
                break;
            case 'experience':
                $query->orderBy('years_experience', 'desc');
                break;
            case 'price_low':
                $query->orderBy('hourly_rate', 'asc');
                break;
            case 'price_high':
                $query->orderBy('hourly_rate', 'desc');
                break;
            default:
                $query->orderBy('rating', 'desc');
        }

        $consultants = $query->paginate(12);
        $specializations = Specialization::active()->ordered()->get();

        return view('consultants.index', compact('consultants', 'specializations'));
    }

    /**
     * Display the specified consultant.
     */
    public function show(Consultant $consultant)
    {
        $consultant->load(['user', 'specializations', 'bookings' => function ($query) {
            $query->where('status', 'completed')->whereNotNull('rating');
        }]);

        // Get available slots for the next 14 days
        $availableSlots = $this->getAvailableSlots($consultant, 14);

        // Get reviews
        $reviews = $consultant->bookings()
            ->where('status', 'completed')
            ->whereNotNull('rating')
            ->with('user')
            ->orderBy('reviewed_at', 'desc')
            ->limit(10)
            ->get();

        return view('consultants.show', compact('consultant', 'availableSlots', 'reviews'));
    }

    /**
     * Get available slots for a consultant.
     */
    private function getAvailableSlots(Consultant $consultant, $days = 14)
    {
        $startDate = Carbon::today();
        $endDate = Carbon::today()->addDays($days);

        $slots = $consultant->availabilitySlots()
            ->available()
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date')
            ->orderBy('start_time')
            ->get()
            ->groupBy(function ($slot) {
                return $slot->date->format('Y-m-d');
            });

        return $slots;
    }

    /**
     * Get available slots for a specific date (AJAX).
     */
    public function getSlots(Request $request, Consultant $consultant)
    {
        $date = $request->get('date');

        if (!$date || !Carbon::parse($date)->isFuture()) {
            return response()->json(['error' => 'Invalid date'], 400);
        }

        $slots = $consultant->availabilitySlots()
            ->available()
            ->where('date', $date)
            ->orderBy('start_time')
            ->get();

        return response()->json($slots);
    }
}
