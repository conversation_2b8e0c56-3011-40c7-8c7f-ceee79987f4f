<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Consultant;
use App\Models\Specialization;
use App\Models\Booking;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        $featuredConsultants = Consultant::with(['user', 'specializations'])
            ->approved()
            ->available()
            ->orderBy('rating', 'desc')
            ->limit(6)
            ->get();

        $specializations = Specialization::active()
            ->ordered()
            ->limit(8)
            ->get();

        $stats = [
            'total_consultants' => Consultant::approved()->count(),
            'total_consultations' => Booking::where('status', 'completed')->count(),
            'total_specializations' => Specialization::active()->count(),
            'average_rating' => Consultant::approved()->avg('rating') ?? 0,
        ];

        return view('home', compact('featuredConsultants', 'specializations', 'stats'));
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        return view('about');
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
        ]);

        // Here you would typically send an email or store the message
        // For now, we'll just redirect back with a success message

        return back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }
}
