<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Booking;
use App\Models\Consultant;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isConsultant()) {
            return $this->consultantDashboard($user);
        } elseif ($user->isAdmin()) {
            return $this->adminDashboard($user);
        } else {
            return $this->userDashboard($user);
        }
    }

    /**
     * User dashboard.
     */
    private function userDashboard($user)
    {
        $upcomingBookings = $user->bookings()
            ->with(['consultant.user', 'availabilitySlot'])
            ->upcoming()
            ->active()
            ->orderBy('scheduled_at')
            ->limit(5)
            ->get();

        $recentBookings = $user->bookings()
            ->with(['consultant.user'])
            ->past()
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get();

        $stats = [
            'total_bookings' => $user->bookings()->count(),
            'completed_consultations' => $user->bookings()->where('status', 'completed')->count(),
            'upcoming_bookings' => $user->bookings()->upcoming()->active()->count(),
            'pending_reviews' => $user->bookings()->where('status', 'completed')->whereNull('rating')->count(),
        ];

        return view('dashboard.user', compact('upcomingBookings', 'recentBookings', 'stats'));
    }

    /**
     * Consultant dashboard.
     */
    private function consultantDashboard($user)
    {
        $consultant = $user->consultant;

        $upcomingBookings = $consultant->bookings()
            ->with(['user', 'availabilitySlot'])
            ->upcoming()
            ->active()
            ->orderBy('scheduled_at')
            ->limit(5)
            ->get();

        $recentBookings = $consultant->bookings()
            ->with(['user'])
            ->past()
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get();

        $stats = [
            'total_consultations' => $consultant->total_consultations,
            'average_rating' => $consultant->rating,
            'upcoming_bookings' => $consultant->bookings()->upcoming()->active()->count(),
            'this_month_earnings' => $consultant->bookings()
                ->where('status', 'completed')
                ->whereMonth('scheduled_at', Carbon::now()->month)
                ->count() * $consultant->hourly_rate * 0.5, // Assuming 30-min sessions
        ];

        return view('dashboard.consultant', compact('upcomingBookings', 'recentBookings', 'stats', 'consultant'));
    }

    /**
     * Admin dashboard.
     */
    private function adminDashboard($user)
    {
        $stats = [
            'total_users' => \App\Models\User::count(),
            'total_consultants' => Consultant::count(),
            'pending_consultants' => Consultant::where('status', 'pending')->count(),
            'total_bookings' => Booking::count(),
            'completed_consultations' => Booking::where('status', 'completed')->count(),
            'revenue_this_month' => Booking::where('status', 'completed')
                ->whereMonth('scheduled_at', Carbon::now()->month)
                ->count() * 50, // Assuming average fee
        ];

        $recentBookings = Booking::with(['user', 'consultant.user'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $pendingConsultants = Consultant::with('user')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('dashboard.admin', compact('stats', 'recentBookings', 'pendingConsultants'));
    }
}
