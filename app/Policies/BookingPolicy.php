<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Booking;
use Illuminate\Auth\Access\HandlesAuthorization;

class BookingPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the booking.
     */
    public function view(User $user, Booking $booking)
    {
        return $user->id === $booking->user_id ||
               $user->id === $booking->consultant->user_id ||
               $user->isAdmin();
    }

    /**
     * Determine whether the user can cancel the booking.
     */
    public function cancel(User $user, Booking $booking)
    {
        return $user->id === $booking->user_id ||
               $user->id === $booking->consultant->user_id ||
               $user->isAdmin();
    }

    /**
     * Determine whether the user can review the booking.
     */
    public function review(User $user, Booking $booking)
    {
        return $user->id === $booking->user_id;
    }
}
