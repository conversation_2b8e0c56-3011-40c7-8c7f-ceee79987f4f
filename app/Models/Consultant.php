<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Consultant extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'bio',
        'experience',
        'education',
        'certifications',
        'languages',
        'hourly_rate',
        'years_experience',
        'profile_image',
        'availability_hours',
        'timezone',
        'status',
        'approved_at',
        'approved_by',
        'rating',
        'total_consultations',
        'is_available',
    ];

    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'years_experience' => 'integer',
        'availability_hours' => 'array',
        'approved_at' => 'datetime',
        'rating' => 'decimal:2',
        'total_consultations' => 'integer',
        'is_available' => 'boolean',
    ];

    /**
     * Get the user that owns the consultant profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the specializations for the consultant.
     */
    public function specializations()
    {
        return $this->belongsToMany(Specialization::class, 'consultant_specializations');
    }

    /**
     * Get the availability slots for the consultant.
     */
    public function availabilitySlots()
    {
        return $this->hasMany(AvailabilitySlot::class);
    }

    /**
     * Get the bookings for the consultant.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the user who approved this consultant.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope a query to only include approved consultants.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include available consultants.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)->where('status', 'approved');
    }

    /**
     * Get the full name of the consultant.
     */
    public function getFullNameAttribute()
    {
        return $this->title ? $this->title . ' ' . $this->user->full_name : $this->user->full_name;
    }

    /**
     * Get available slots for a specific date.
     */
    public function getAvailableSlotsForDate($date)
    {
        return $this->availabilitySlots()
            ->where('date', $date)
            ->where('status', 'available')
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Check if consultant is available on a specific date and time.
     */
    public function isAvailableAt($date, $startTime, $endTime)
    {
        return $this->availabilitySlots()
            ->where('date', $date)
            ->where('start_time', '<=', $startTime)
            ->where('end_time', '>=', $endTime)
            ->where('status', 'available')
            ->exists();
    }
}
