<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AvailabilitySlot extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultant_id',
        'date',
        'start_time',
        'end_time',
        'is_available',
        'status',
        'notes',
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'is_available' => 'boolean',
    ];

    /**
     * Get the consultant that owns the availability slot.
     */
    public function consultant()
    {
        return $this->belongsTo(Consultant::class);
    }

    /**
     * Get the booking for this slot.
     */
    public function booking()
    {
        return $this->hasOne(Booking::class);
    }

    /**
     * Scope a query to only include available slots.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available')->where('is_available', true);
    }

    /**
     * Scope a query to only include future slots.
     */
    public function scopeFuture($query)
    {
        return $query->where('date', '>=', Carbon::today());
    }

    /**
     * Scope a query for a specific date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Get the duration in minutes.
     */
    public function getDurationAttribute()
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);
        return $end->diffInMinutes($start);
    }

    /**
     * Get formatted time range.
     */
    public function getTimeRangeAttribute()
    {
        return Carbon::parse($this->start_time)->format('H:i') . ' - ' . Carbon::parse($this->end_time)->format('H:i');
    }

    /**
     * Check if this slot can accommodate a booking of given duration.
     */
    public function canAccommodate($durationMinutes)
    {
        return $this->duration >= $durationMinutes && $this->status === 'available';
    }

    /**
     * Mark slot as booked.
     */
    public function markAsBooked()
    {
        $this->update([
            'status' => 'booked',
            'is_available' => false,
        ]);
    }

    /**
     * Mark slot as available.
     */
    public function markAsAvailable()
    {
        $this->update([
            'status' => 'available',
            'is_available' => true,
        ]);
    }
}
