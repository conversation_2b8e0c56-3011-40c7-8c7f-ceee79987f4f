<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_number',
        'user_id',
        'consultant_id',
        'availability_slot_id',
        'scheduled_at',
        'duration_minutes',
        'status',
        'consultation_topic',
        'user_notes',
        'consultant_notes',
        'cancellation_reason',
        'cancelled_at',
        'cancelled_by',
        'rating',
        'review',
        'reviewed_at',
        'meeting_details',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'duration_minutes' => 'integer',
        'cancelled_at' => 'datetime',
        'rating' => 'decimal:2',
        'reviewed_at' => 'datetime',
        'meeting_details' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_number)) {
                $booking->booking_number = 'BK' . strtoupper(uniqid());
            }
        });
    }

    /**
     * Get the user that owns the booking.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the consultant for the booking.
     */
    public function consultant()
    {
        return $this->belongsTo(Consultant::class);
    }

    /**
     * Get the availability slot for the booking.
     */
    public function availabilitySlot()
    {
        return $this->belongsTo(AvailabilitySlot::class);
    }

    /**
     * Get the user who cancelled the booking.
     */
    public function cancelledBy()
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    /**
     * Scope a query to only include upcoming bookings.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_at', '>', Carbon::now());
    }

    /**
     * Scope a query to only include past bookings.
     */
    public function scopePast($query)
    {
        return $query->where('scheduled_at', '<', Carbon::now());
    }

    /**
     * Scope a query to only include active bookings.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'confirmed']);
    }

    /**
     * Check if booking can be cancelled.
     */
    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'confirmed']) &&
               $this->scheduled_at > Carbon::now()->addHours(2);
    }

    /**
     * Check if booking can be reviewed.
     */
    public function canBeReviewed()
    {
        return $this->status === 'completed' && is_null($this->rating);
    }

    /**
     * Cancel the booking.
     */
    public function cancel($reason = null, $cancelledBy = null)
    {
        $this->update([
            'status' => 'cancelled',
            'cancellation_reason' => $reason,
            'cancelled_at' => Carbon::now(),
            'cancelled_by' => $cancelledBy,
        ]);

        // Mark availability slot as available again
        $this->availabilitySlot->markAsAvailable();
    }

    /**
     * Confirm the booking.
     */
    public function confirm()
    {
        $this->update(['status' => 'confirmed']);
    }

    /**
     * Complete the booking.
     */
    public function complete()
    {
        $this->update(['status' => 'completed']);

        // Update consultant's total consultations
        $this->consultant->increment('total_consultations');
    }

    /**
     * Add review to the booking.
     */
    public function addReview($rating, $review = null)
    {
        $this->update([
            'rating' => $rating,
            'review' => $review,
            'reviewed_at' => Carbon::now(),
        ]);

        // Update consultant's average rating
        $this->updateConsultantRating();
    }

    /**
     * Update consultant's average rating.
     */
    private function updateConsultantRating()
    {
        $averageRating = $this->consultant->bookings()
            ->whereNotNull('rating')
            ->avg('rating');

        $this->consultant->update(['rating' => round($averageRating, 2)]);
    }

    /**
     * Get formatted scheduled time.
     */
    public function getFormattedScheduledTimeAttribute()
    {
        return $this->scheduled_at->format('M j, Y \a\t g:i A');
    }

    /**
     * Get status badge color.
     */
    public function getStatusColorAttribute()
    {
        switch($this->status) {
            case 'pending':
                return 'yellow';
            case 'confirmed':
                return 'blue';
            case 'completed':
                return 'green';
            case 'cancelled':
                return 'red';
            case 'no_show':
                return 'gray';
            default:
                return 'gray';
        }
    }
}
